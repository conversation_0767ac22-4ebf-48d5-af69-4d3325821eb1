import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api/auth'
import { getToken, setToken, removeToken } from '@/utils/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref(getToken())
  const userInfo = ref(null)
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!userInfo.value)

  // 登录
  const login = async (loginForm) => {
    loading.value = true
    try {
      const response = await authApi.login(loginForm)
      const { accessToken, adminInfo } = response.data
      
      token.value = accessToken
      userInfo.value = adminInfo
      setToken(accessToken)
      
      return response
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      token.value = null
      userInfo.value = null
      removeToken()
    }
  }

  // 检查认证状态
  const checkAuth = async () => {
    const savedToken = getToken()
    if (!savedToken) {
      return false
    }

    try {
      const response = await authApi.getCurrentUser()
      // 新的接口返回完整的用户档案信息
      userInfo.value = {
        adminId: response.data.adminId,
        username: response.data.username,
        realName: response.data.realName,
        email: response.data.email,
        phone: response.data.phone,
        avatar: response.data.avatar,
        role: response.data.role,
        roleName: response.data.roleName
      }
      token.value = savedToken
      return true
    } catch (error) {
      console.error('检查认证状态失败:', error)

      // 只有在明确是401错误时才清除认证状态
      if (error.response?.status === 401) {
        removeToken()
        token.value = null
        userInfo.value = null
        return false
      }

      // 其他错误（如网络错误）不清除认证状态
      // 保持当前token，让用户可以继续使用
      token.value = savedToken
      return true
    }
  }

  // 更新用户信息（本地更新）
  const updateUserInfo = (newUserInfo) => {
    userInfo.value = { ...userInfo.value, ...newUserInfo }
  }

  // 更新用户资料（调用API）
  const updateProfile = async (profileData) => {
    loading.value = true
    try {
      // 调用更新用户资料的API
      const response = await authApi.updateProfile(profileData)

      if (response.code === 200) {
        // 更新本地用户信息
        updateUserInfo(profileData)
        return { success: true, message: '用户信息更新成功' }
      } else {
        throw new Error(response.message || '更新失败')
      }
    } catch (error) {
      console.error('更新用户信息失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 修改密码
  const changePassword = async (passwordData) => {
    loading.value = true
    try {
      // 调用修改密码的API
      const response = await authApi.changePassword(passwordData)

      if (response.code === 200) {
        // 密码修改成功后，立即清除本地认证状态
        token.value = null
        userInfo.value = null
        removeToken()

        return { success: true, message: '密码修改成功' }
      } else {
        throw new Error(response.message || '密码修改失败')
      }
    } catch (error) {
      console.error('修改密码失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    token,
    userInfo,
    loading,
    isAuthenticated,
    login,
    logout,
    checkAuth,
    updateUserInfo,
    updateProfile,
    changePassword
  }
})
