package com.zhisuo.manager.util;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zhisuo.manager.entity.Admin;
import com.zhisuo.manager.mapper.AdminMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.bcrypt.BCrypt;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 密码重置工具类
 * 用于重置admin用户的密码为admin123
 */
@Slf4j
@Component
public class PasswordResetUtil implements CommandLineRunner {

    @Autowired
    private AdminMapper adminMapper;

    @Override
    public void run(String... args) throws Exception {
        // 检查是否需要重置密码
        if (args.length > 0 && "reset-admin-password".equals(args[0])) {
            resetAdminPassword();
        }
    }

    public void resetAdminPassword() {
        try {
            String username = "admin";
            String password = "admin123";
            
            // 生成新的密码哈希
            String passwordHash = BCrypt.hashpw(password, BCrypt.gensalt());
            
            log.info("重置admin用户密码");
            log.info("新密码: {}", password);
            log.info("新密码哈希: {}", passwordHash);
            
            // 更新数据库
            LambdaUpdateWrapper<Admin> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Admin::getUsername, username)
                    .set(Admin::getPasswordHash, passwordHash)
                    .set(Admin::getUpdateTime, LocalDateTime.now());
            
            int result = adminMapper.update(null, updateWrapper);
            
            if (result > 0) {
                log.info("admin用户密码重置成功");
                
                // 验证密码是否正确
                boolean isValid = BCrypt.checkpw(password, passwordHash);
                log.info("密码验证结果: {}", isValid);
            } else {
                log.error("admin用户密码重置失败");
            }
            
        } catch (Exception e) {
            log.error("重置密码时发生错误", e);
        }
    }
}
