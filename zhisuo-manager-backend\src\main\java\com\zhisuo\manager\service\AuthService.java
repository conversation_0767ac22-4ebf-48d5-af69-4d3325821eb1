package com.zhisuo.manager.service;

import com.zhisuo.manager.dto.LoginRequest;
import com.zhisuo.manager.dto.UserStatsDTO;
import com.zhisuo.manager.entity.Admin;
import com.zhisuo.manager.vo.LoginResponse;

import javax.servlet.http.HttpServletRequest;

/**
 * 认证服务接口
 */
public interface AuthService {
    
    /**
     * 管理员登录
     *
     * @param request 登录请求
     * @return 登录响应
     */
    LoginResponse login(LoginRequest request);
    
    /**
     * 管理员登出
     *
     * @param token 令牌
     */
    void logout(String token);
    
    /**
     * 根据用户名获取管理员信息
     *
     * @param username 用户名
     * @return 管理员信息
     */
    Admin getAdminByUsername(String username);
    
    /**
     * 根据ID获取管理员信息
     *
     * @param adminId 管理员ID
     * @return 管理员信息
     */
    Admin getAdminById(String adminId);
    
    /**
     * 更新最后登录时间和IP
     *
     * @param adminId 管理员ID
     * @param loginIp 登录IP
     */
    void updateLastLogin(String adminId, String loginIp);

    /**
     * 获取用户统计数据
     *
     * @param username 用户名
     * @param request HTTP请求
     * @return 用户统计数据
     */
    UserStatsDTO getUserStats(String username, HttpServletRequest request);

    /**
     * 更新用户资料
     *
     * @param username 用户名
     * @param admin 管理员信息
     */
    void updateProfile(String username, Admin admin);

    /**
     * 修改密码
     *
     * @param username 用户名
     * @param currentPassword 当前密码
     * @param newPassword 新密码
     */
    void changePassword(String username, String currentPassword, String newPassword);

    /**
     * 重置admin密码（仅用于开发调试）
     */
    void resetAdminPassword();
}
