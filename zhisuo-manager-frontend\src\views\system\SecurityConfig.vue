<template>
  <div class="security-config-page">
    <div class="page-header">
      <h2>安全配置</h2>
      <p>配置系统安全策略和访问控制</p>
    </div>
    
    <div class="content-card">
      <div class="coming-soon">
        <el-icon size="64" color="#ccc"><Lock /></el-icon>
        <h3>安全配置功能开发中...</h3>
        <p>即将推出密码策略、登录安全等配置功能</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Lock } from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
.security-config-page {
  padding: 24px;
  
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin: 0 0 8px 0;
    }
    
    p {
      color: #666;
      margin: 0;
      font-size: 14px;
    }
  }
  
  .content-card {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }
  
  .coming-soon {
    text-align: center;
    padding: 80px 20px;
    
    h3 {
      margin: 20px 0 10px 0;
      color: #666;
    }
    
    p {
      color: #999;
      margin: 0;
    }
  }
}
</style>
