package com.zhisuo.app.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import java.util.Set;
import java.util.Map;
import java.util.HashMap;
import java.util.Arrays;

/**
 * Redis工具类
 */
@Component
public class RedisUtil {
    
    private final RedisTemplate<String, Object> redisTemplate;
    
    // 当天热度值前缀
    public static final String HOT_TOPIC_VIEW_COUNT_PREFIX = "hot_topic:view:";
    public static final String HOT_TOPIC_SEARCH_COUNT_PREFIX = "hot_topic:search:";
    public static final String HOT_TOPIC_HOT_VALUE_PREFIX = "hot_topic:hot_value:current:";

    // 热度值保存时间（秒）- 2天
    public static final long HOT_VALUE_EXPIRE_TIME = 2 * 24 * 60 * 60;
    
    @Autowired
    public RedisUtil(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
    
    /**
     * 设置指定key的值
     *
     * @param key   键
     * @param value 值
     */
    public void set(String key, Object value) {
        redisTemplate.opsForValue().set(key, value);
    }
    
    /**
     * 设置指定key的值，并设置过期时间
     *
     * @param key     键
     * @param value   值
     * @param timeout 过期时间(秒)
     */
    public void set(String key, Object value, long timeout) {
        redisTemplate.opsForValue().set(key, value, timeout, TimeUnit.SECONDS);
    }
    
    /**
     * 获取指定key的值
     *
     * @param key 键
     * @return 值
     */
    public Object get(String key) {
        return redisTemplate.opsForValue().get(key);
    }
    
    /**
     * 删除指定key
     *
     * @param key 键
     */
    public Boolean delete(String key) {
        return redisTemplate.delete(key);
    }

    /**
     * 批量删除key
     *
     * @param keys 键数组
     */
    public Long delete(String... keys) {
        if (keys == null || keys.length == 0) {
            return 0L;
        }
        return redisTemplate.delete(Arrays.asList(keys));
    }

    /**
     * 根据模式获取所有匹配的key
     *
     * @param pattern 模式
     * @return 匹配的key集合
     */
    public Set<String> keys(String pattern) {
        return redisTemplate.keys(pattern);
    }
    
    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return 是否存在
     */
    public Boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * 增加指定key的值
     *
     * @param key   键
     * @param delta 增加的值(大于0)
     * @return 增加后的值
     */
    public Long increment(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递增因子必须大于0");
        }
        return redisTemplate.opsForValue().increment(key, delta);
    }

    /**
     * 增加热点话题浏览量
     *
     * @param topicId 话题ID
     * @return 增加后的浏览量
     */
    public Integer incrementHotTopicViewCount(String topicId) {
        String key = HOT_TOPIC_VIEW_COUNT_PREFIX + topicId;
        Long result = increment(key, 1);
        return result != null ? result.intValue() : 0;
    }

    /**
     * 增加热点话题搜索量
     *
     * @param topicId 话题ID
     * @return 增加后的搜索量
     */
    public Integer incrementHotTopicSearchCount(String topicId) {
        String key = HOT_TOPIC_SEARCH_COUNT_PREFIX + topicId;
        Long result = increment(key, 1);
        return result != null ? result.intValue() : 0;
    }

    /**
     * 保存热点话题的热度值
     *
     * @param topicId  话题ID
     * @param hotValue 热度值
     * @param expireTime 过期时间(秒)
     */
    public void saveHotTopicHotValue(String topicId, String hotValue, long expireTime) {
        String key = HOT_TOPIC_HOT_VALUE_PREFIX + topicId;
        set(key, hotValue, expireTime);
    }
    
    /**
     * 获取所有需要同步到数据库的热点话题浏览量
     * 
     * @return 话题ID到浏览量的映射
     */
    public Map<String, Integer> getAllHotTopicViewCounts() {
        Set<String> keys = redisTemplate.keys(HOT_TOPIC_VIEW_COUNT_PREFIX + "*");
        Map<String, Integer> result = new HashMap<>();
        
        if (keys != null) {
            for (String key : keys) {
                String topicId = key.substring(HOT_TOPIC_VIEW_COUNT_PREFIX.length());
                Object value = get(key);
                if (value != null) {
                    // 安全处理类型转换
                    if (value instanceof Integer) {
                        result.put(topicId, (Integer) value);
                    } else if (value instanceof Long) {
                        result.put(topicId, ((Long) value).intValue());
                    } else {
                        result.put(topicId, Integer.parseInt(value.toString()));
                    }
                }
            }
        }
        
        return result;
    }
    
    /**
     * 获取所有需要同步到数据库的热点话题搜索量
     * 
     * @return 话题ID到搜索量的映射
     */
    public Map<String, Integer> getAllHotTopicSearchCounts() {
        Set<String> keys = redisTemplate.keys(HOT_TOPIC_SEARCH_COUNT_PREFIX + "*");
        Map<String, Integer> result = new HashMap<>();
        
        if (keys != null) {
            for (String key : keys) {
                String topicId = key.substring(HOT_TOPIC_SEARCH_COUNT_PREFIX.length());
                Object value = get(key);
                if (value != null) {
                    // 安全处理类型转换
                    if (value instanceof Integer) {
                        result.put(topicId, (Integer) value);
                    } else if (value instanceof Long) {
                        result.put(topicId, ((Long) value).intValue());
                    } else {
                        result.put(topicId, Integer.parseInt(value.toString()));
                    }
                }
            }
        }
        
        return result;
    }

    /**
     * 保存热点话题热度值（按日期）
     *
     * @param topicId 话题ID
     * @param date 日期
     * @param hotValue 热度值
     * @param expireTime 过期时间
     * @param timeUnit 时间单位
     */
    public void saveHotValueByDate(String topicId, Date date, String hotValue, long expireTime, TimeUnit timeUnit) {
        // 将日期格式化为yyyyMMdd格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(date);

        // 构建Redis键：hot_topic:hot_value:日期:话题ID
        String key = "hot_topic:hot_value:" + dateStr + ":" + topicId;

        // 保存热度值，设置自定义过期时间
        redisTemplate.opsForValue().set(key, hotValue, expireTime, timeUnit);
    }

    /**
     * 获取热点话题指定日期的热度值
     * 
     * @param topicId 话题ID
     * @param date 日期
     * @return 热度值，如果不存在则返回null
     */
    public String getHotValueByDate(String topicId, Date date) {
        // 将日期格式化为yyyyMMdd格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(date);
        
        // 构建Redis键：hot_topic:hot_value:日期:话题ID
        String key = "hot_topic:hot_value:" + dateStr + ":" + topicId;
        
        // 获取热度值
        return (String) redisTemplate.opsForValue().get(key);
    }
} 