// 全局样式文件

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// 布局样式
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  height: 64px;
  background: #FFFFFF;
  border-bottom: 1px solid #E5E7EB;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.layout-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.layout-sidebar {
  width: 240px;
  background: #2D3748;
  overflow-y: auto;
}

.layout-main {
  flex: 1;
  background: #F8FAFC;
  overflow-y: auto;
}

// 卡片样式
.content-card {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #F3F4F6;
}

// 表格样式
.table-container {
  .el-table {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #E5E7EB;

    .el-table__header {
      background-color: #F9FAFB;

      th {
        background-color: #F9FAFB !important;
        color: #374151;
        font-weight: 600;
        border-bottom: 1px solid #E5E7EB;
      }
    }

    .el-table__row {
      &:hover {
        background-color: #F9FAFB;
      }
    }
  }

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .table-title {
      font-size: 18px;
      font-weight: 600;
      color: #1F2937;
    }

    .table-actions {
      display: flex;
      gap: 12px;
    }
  }
}

// 表单样式
.form-container {
  .el-form {
    max-width: 600px;
  }
  
  .form-footer {
    text-align: center;
    margin-top: 30px;
  }
}

// 页面头部样式
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;

  .header-left {
    h2 {
      font-size: 28px;
      font-weight: 700;
      color: #1F2937;
      margin: 0 0 8px 0;
    }

    p {
      color: #6B7280;
      margin: 0;
      font-size: 16px;
    }
  }

  .header-right {
    .date-info {
      color: #6B7280;
      font-size: 14px;
      background: #FFFFFF;
      padding: 8px 16px;
      border-radius: 8px;
      border: 1px solid #E5E7EB;
    }
  }
}

// 统计卡片样式
.stat-card {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #F3F4F6;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  .stat-title {
    font-size: 14px;
    color: #6B7280;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .stat-value {
    font-size: 32px;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 8px;
    line-height: 1;
  }

  .stat-trend {
    display: flex;
    align-items: center;
    gap: 8px;

    .trend-text {
      font-size: 12px;
      color: #9CA3AF;
    }

    .trend-value {
      font-size: 12px;
      font-weight: 600;

      &.positive {
        color: #10B981;
      }

      &.negative {
        color: #EF4444;
      }
    }
  }
}

// 响应式
@media (max-width: 768px) {
  .layout-sidebar {
    width: 64px;
  }
  
  .layout-main {
    padding: 10px;
  }
  
  .content-card {
    padding: 15px;
  }
}

// 工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-10 { margin-bottom: 10px; }
.mb-20 { margin-bottom: 20px; }
.mb-30 { margin-bottom: 30px; }

.mt-10 { margin-top: 10px; }
.mt-20 { margin-top: 20px; }
.mt-30 { margin-top: 30px; }

.flex { display: flex; }
.flex-center { display: flex; align-items: center; justify-content: center; }
.flex-between { display: flex; align-items: center; justify-content: space-between; }

.full-width { width: 100%; }
.full-height { height: 100%; }

// 按钮样式增强
.el-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-1px);
  }

  &.el-button--primary {
    background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
    border-color: #8B5CF6;

    &:hover {
      background: linear-gradient(135deg, #7C3AED 0%, #6D28D9 100%);
      border-color: #7C3AED;
    }
  }
}

// 输入框样式增强
.el-input {
  .el-input__wrapper {
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #8B5CF6;
    }

    &.is-focus {
      border-color: #8B5CF6;
      box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.1);
    }
  }
}

// 选择器样式增强
.el-select {
  .el-input__wrapper {
    border-radius: 8px;
  }
}

// 分页样式增强
.el-pagination {
  .el-pager li {
    border-radius: 6px;
    margin: 0 2px;

    &.is-active {
      background: #8B5CF6;
      color: white;
    }
  }

  .btn-prev,
  .btn-next {
    border-radius: 6px;
  }
}

// 标签样式
.status-tag {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;

  &.published {
    background-color: #D1FAE5;
    color: #065F46;
    border: 1px solid #A7F3D0;
  }

  &.draft {
    background-color: #FEF3C7;
    color: #92400E;
    border: 1px solid #FDE68A;
  }
}

// Element Plus 悬浮菜单样式覆盖
.el-popper.is-light[role="tooltip"] {
  background: #FFFFFF !important;
  border: 1px solid #E5E7EB !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  padding: 8px !important;
  min-width: 160px !important;

  .el-menu--popup-container {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;

    .el-menu-item {
      height: 40px !important;
      line-height: 1 !important;
      margin: 2px 0 !important;
      padding: 0 12px !important;
      border-radius: 6px !important;
      font-size: 14px !important;
      color: #374151 !important;
      background: transparent !important;
      display: flex !important;
      align-items: center !important;
      justify-content: flex-start !important;
      transition: all 0.2s ease !important;

      &:hover {
        background: #F3F4F6 !important;
        color: #1F2937 !important;

        .el-icon {
          color: #8B5CF6 !important;
        }
      }

      &.is-active {
        background: #8B5CF6 !important;
        color: #FFFFFF !important;

        .el-icon {
          color: #FFFFFF !important;
        }

        span {
          color: #FFFFFF !important;
        }
      }

      .el-icon {
        color: #6B7280 !important;
        font-size: 16px !important;
        margin-right: 8px !important;
        flex-shrink: 0 !important;
        transition: color 0.2s ease !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 16px !important;
        height: 16px !important;
      }

      span {
        flex: 1 !important;
        text-align: left !important;
        font-weight: 400 !important;
        color: inherit !important;
        display: flex !important;
        align-items: center !important;
        height: 100% !important;
      }
    }
  }
}

// 更强的选择器覆盖
.el-tooltip__popper.is-light {
  background: #FFFFFF !important;
  border: 1px solid #E5E7EB !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  padding: 8px !important;
  min-width: 160px !important;
}

.el-tooltip__popper.is-light .el-menu--popup-container .el-menu-item {
  height: 40px !important;
  line-height: 1 !important;
  margin: 2px 0 !important;
  padding: 0 12px !important;
  border-radius: 6px !important;
  font-size: 14px !important;
  color: #374151 !important;
  background: transparent !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  transition: all 0.2s ease !important;
}

.el-tooltip__popper.is-light .el-menu--popup-container .el-menu-item:hover {
  background: #F3F4F6 !important;
  color: #1F2937 !important;
}

.el-tooltip__popper.is-light .el-menu--popup-container .el-menu-item:hover .el-icon {
  color: #8B5CF6 !important;
}

.el-tooltip__popper.is-light .el-menu--popup-container .el-menu-item.is-active {
  background: #8B5CF6 !important;
  color: #FFFFFF !important;
}

.el-tooltip__popper.is-light .el-menu--popup-container .el-menu-item.is-active .el-icon {
  color: #FFFFFF !important;
}

.el-tooltip__popper.is-light .el-menu--popup-container .el-menu-item.is-active span {
  color: #FFFFFF !important;
}

.el-tooltip__popper.is-light .el-menu--popup-container .el-menu-item .el-icon {
  color: #6B7280 !important;
  font-size: 16px !important;
  margin-right: 8px !important;
  flex-shrink: 0 !important;
  transition: color 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 16px !important;
  height: 16px !important;
}

.el-tooltip__popper.is-light .el-menu--popup-container .el-menu-item span {
  flex: 1 !important;
  text-align: left !important;
  font-weight: 400 !important;
  color: inherit !important;
  display: flex !important;
  align-items: center !important;
  height: 100% !important;
}
