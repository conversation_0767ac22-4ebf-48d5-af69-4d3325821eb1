<template>
  <div class="personal-center">
    <!-- 页面标题 -->
    <h1>个人中心</h1>

    <!-- 顶部信息卡片 -->
    <div class="profile-header">
      <div class="profile-info">
        <div class="avatar-section">
          <el-avatar :size="80" :src="userInfo?.avatar" class="user-avatar">
            <el-icon size="40"><UserFilled /></el-icon>
          </el-avatar>
        </div>
        
        <div class="user-details">
          <h2 class="username">{{ userInfo?.realName || '未设置' }}</h2>
          <p class="user-role">{{ userInfo?.roleName || '管理员' }}</p>
          <p class="user-id">ID: {{ userInfo?.adminId || userInfo?.userId || '--' }}</p>
        </div>
      </div>
      
      <div class="profile-stats">
        <div class="stat-item">
          <div class="stat-label">账号完整度</div>
          <div class="stat-value">
            <span class="percentage">{{ profileCompleteness }}%</span>
            <el-progress :percentage="profileCompletenessValue" :show-text="false" :stroke-width="6" color="#8B5CF6" />
            <span class="status">{{ profileCompletenessStatus }}</span>
          </div>
        </div>

        <div class="stat-item">
          <div class="stat-label">最后登录</div>
          <div class="stat-value">
            <span class="date">{{ lastLoginDate || '--' }}</span>
            <span class="time">{{ lastLoginTime || '--' }}</span>
          </div>
        </div>

        <div class="stat-item">
          <div class="stat-label">操作系统</div>
          <div class="stat-value">
            <span class="os">{{ userAgent.os || '--' }}</span>
            <span class="browser">{{ userAgent.browser || '--' }}</span>
          </div>
        </div>

        <div class="stat-item">
          <div class="stat-label">最后使用时间</div>
          <div class="stat-value">
            <span class="date">{{ lastActiveDate || '--' }}</span>
            <span class="status" :class="{ online: isOnline }">{{ onlineStatus }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <div class="content-left">
        <!-- 导航标签 -->
        <div class="nav-tabs">
          <button 
            :class="['tab-btn', { active: activeTab === 'basic' }]"
            @click="activeTab = 'basic'"
          >
            基本设置
          </button>
          <button 
            :class="['tab-btn', { active: activeTab === 'modify' }]"
            @click="activeTab = 'modify'"
          >
            修改设置
          </button>
        </div>

        <!-- 基本设置表单 -->
        <div v-show="activeTab === 'basic'" class="form-section">
          <el-form
            ref="basicForm"
            :model="formData"
            :rules="rules"
            label-width="80px"
            class="profile-form"
          >
            <el-form-item label="用户名" prop="username">
              <el-input
                v-model="formData.username"
                placeholder="请输入用户名"
                :disabled="!editMode"
              />
            </el-form-item>

            <el-form-item label="真实姓名" prop="realName">
              <el-input
                v-model="formData.realName"
                placeholder="请输入真实姓名"
                :disabled="!editMode"
              />
            </el-form-item>

            <el-form-item label="手机号" prop="phone">
              <el-input
                v-model="formData.phone"
                placeholder="请输入手机号"
                :disabled="!editMode"
              />
            </el-form-item>

            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="formData.email"
                placeholder="请输入邮箱地址"
                :disabled="!editMode"
              />
            </el-form-item>
          </el-form>

          <div class="form-actions">
            <el-button 
              v-if="!editMode"
              type="primary" 
              @click="editMode = true"
            >
              编辑
            </el-button>
            <template v-else>
              <el-button 
                type="primary" 
                :loading="saving"
                @click="handleSave"
              >
                保存更改
              </el-button>
              <el-button @click="handleCancel">
                取消
              </el-button>
            </template>
          </div>
        </div>

        <!-- 修改设置表单 -->
        <div v-show="activeTab === 'modify'" class="form-section">
          <el-form
            ref="passwordForm"
            :model="passwordData"
            :rules="passwordRules"
            label-width="80px"
            class="profile-form"
          >
            <el-form-item label="当前密码" prop="currentPassword">
              <el-input
                v-model="passwordData.currentPassword"
                type="password"
                placeholder="请输入当前密码"
                show-password
              />
            </el-form-item>

            <el-form-item label="新密码" prop="newPassword">
              <el-input
                v-model="passwordData.newPassword"
                type="password"
                placeholder="请输入新密码"
                show-password
              />
            </el-form-item>

            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="passwordData.confirmPassword"
                type="password"
                placeholder="请再次输入新密码"
                show-password
              />
            </el-form-item>
          </el-form>

          <div class="form-actions">
            <el-button 
              type="primary" 
              :loading="changingPassword"
              @click="handlePasswordChange"
            >
              修改密码
            </el-button>
            <el-button @click="resetPasswordForm">
              重置
            </el-button>
          </div>
        </div>
      </div>

      <!-- 右侧头像上传区域 -->
      <div class="content-right">
        <div class="avatar-upload-section">
          <div class="upload-area" @click="handleAvatarClick">
            <el-icon class="upload-icon"><Upload /></el-icon>
            <div class="upload-text">
              <p>更换头像</p>
              <p class="upload-hint">支持 JPG、PNG 格式，不超过 2MB</p>
            </div>
          </div>
          
          <!-- 隐藏的文件输入 -->
          <input
            ref="avatarInput"
            type="file"
            accept="image/*"
            style="display: none"
            @change="handleAvatarChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { Upload, UserFilled } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { uploadApi } from '@/api/upload'
import { authApi } from '@/api/auth'
import { useRouter } from 'vue-router'

const authStore = useAuthStore()
const router = useRouter()

// 响应式数据
const activeTab = ref('basic')
const editMode = ref(false)
const saving = ref(false)
const changingPassword = ref(false)
const avatarInput = ref(null)
const basicForm = ref(null)
const passwordForm = ref(null)
const loading = ref(false)

// 用户信息
const userInfo = computed(() => authStore.userInfo)

// 动态统计数据
const profileStats = ref({
  lastLoginTime: null,
  lastActiveTime: null,
  userAgent: '',
  isOnline: false
})

// 计算属性 - 账号完整度
const profileCompletenessValue = computed(() => {
  if (!userInfo.value) return 0

  let completeness = 0
  const fields = ['username', 'realName', 'email', 'phone', 'avatar']
  const weights = { username: 20, realName: 20, email: 20, phone: 20, avatar: 20 }

  fields.forEach(field => {
    if (userInfo.value[field] && userInfo.value[field].trim()) {
      completeness += weights[field]
    }
  })

  return completeness
})

const profileCompleteness = computed(() => profileCompletenessValue.value)

const profileCompletenessStatus = computed(() => {
  const value = profileCompletenessValue.value
  if (value >= 80) return '优秀'
  if (value >= 60) return '良好'
  if (value >= 40) return '中等'
  return '较低'
})

// 计算属性 - 最后登录时间
const lastLoginDate = computed(() => {
  if (!profileStats.value.lastLoginTime) return null
  return new Date(profileStats.value.lastLoginTime).toLocaleDateString('zh-CN')
})

const lastLoginTime = computed(() => {
  if (!profileStats.value.lastLoginTime) return null
  return new Date(profileStats.value.lastLoginTime).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
})

// 计算属性 - 最后活跃时间
const lastActiveDate = computed(() => {
  if (!profileStats.value.lastActiveTime) return null
  return new Date(profileStats.value.lastActiveTime).toLocaleDateString('zh-CN')
})

// 计算属性 - 在线状态
const isOnline = computed(() => profileStats.value.isOnline)

const onlineStatus = computed(() => isOnline.value ? '在线' : '离线')

// 计算属性 - 用户代理信息
const userAgent = computed(() => {
  const ua = profileStats.value.userAgent || navigator.userAgent
  return parseUserAgent(ua)
})

// 表单数据
const formData = reactive({
  username: '',
  realName: '',
  phone: '',
  email: ''
})

// 原始数据备份
const originalData = reactive({})

// 密码修改数据
const passwordData = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 密码验证规则
const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordData.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 解析用户代理信息
const parseUserAgent = (userAgent) => {
  const ua = userAgent.toLowerCase()

  // 检测操作系统
  let os = '未知'
  if (ua.includes('windows nt 10')) os = 'Windows 10'
  else if (ua.includes('windows nt 6.3')) os = 'Windows 8.1'
  else if (ua.includes('windows nt 6.2')) os = 'Windows 8'
  else if (ua.includes('windows nt 6.1')) os = 'Windows 7'
  else if (ua.includes('windows')) os = 'Windows'
  else if (ua.includes('mac os x')) os = 'macOS'
  else if (ua.includes('linux')) os = 'Linux'
  else if (ua.includes('android')) os = 'Android'
  else if (ua.includes('iphone') || ua.includes('ipad')) os = 'iOS'

  // 检测浏览器
  let browser = '未知'
  if (ua.includes('edg/')) browser = 'Edge'
  else if (ua.includes('chrome/') && !ua.includes('edg/')) browser = 'Chrome'
  else if (ua.includes('firefox/')) browser = 'Firefox'
  else if (ua.includes('safari/') && !ua.includes('chrome/')) browser = 'Safari'
  else if (ua.includes('opera/') || ua.includes('opr/')) browser = 'Opera'

  return { os, browser }
}

// 获取用户统计数据（从当前用户信息中提取）
const fetchUserStats = async () => {
  try {
    loading.value = true

    // 调用统一的用户信息接口
    const response = await authApi.getCurrentUser()

    if (response.code === 200 && response.data && response.data.stats) {
      profileStats.value = {
        lastLoginTime: response.data.stats.lastLoginTime,
        lastActiveTime: response.data.stats.lastActiveTime,
        userAgent: response.data.stats.userAgent || navigator.userAgent,
        isOnline: response.data.stats.isOnline || false
      }
    } else {
      throw new Error(response.message || 'API返回错误')
    }

  } catch (error) {
    console.error('获取用户统计数据失败:', error)

    // 使用默认数据，不要因为API失败就跳转登录
    profileStats.value = {
      lastLoginTime: new Date().getTime() - 24 * 60 * 60 * 1000,
      lastActiveTime: new Date().getTime() - 2 * 60 * 60 * 1000,
      userAgent: navigator.userAgent,
      isOnline: false
    }

    // 只有在明确是401错误时才提示重新登录
    if (error.response?.status === 401) {
      console.warn('统计数据API认证失败，但不强制跳转登录')
    }

  } finally {
    loading.value = false
  }
}

// 初始化表单数据
const initFormData = () => {
  if (userInfo.value) {
    formData.username = userInfo.value.username || ''
    formData.realName = userInfo.value.realName || ''
    formData.phone = userInfo.value.phone || ''
    formData.email = userInfo.value.email || ''
  }

  // 备份原始数据
  Object.assign(originalData, formData)
}

// 监听用户信息变化，自动更新表单数据
watch(userInfo, (newUserInfo) => {
  if (newUserInfo) {
    initFormData()
  }
}, { immediate: true })

// 头像点击处理
const handleAvatarClick = () => {
  avatarInput.value?.click()
}

// 头像文件选择处理
const handleAvatarChange = async (event) => {
  const file = event.target.files[0]
  if (!file) return

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return
  }

  // 验证文件大小（2MB）
  if (file.size > 2 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过2MB')
    return
  }

  let loadingInstance = null
  try {
    // 使用 ElLoading 服务显示加载状态
    loadingInstance = ElLoading.service({
      lock: true,
      text: '正在上传头像...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    // 上传头像
    const response = await uploadApi.uploadAvatar(file)

    if (response.code === 200) {
      // 上传成功后，调用后端接口保存头像URL到数据库
      await authStore.updateProfile({ avatar: response.data.url })
      ElMessage.success('头像更新成功')
    } else {
      ElMessage.error(response.message || '头像上传失败')
    }
  } catch (error) {
    console.error('头像上传失败:', error)
    ElMessage.error('头像上传失败: ' + (error.message || '未知错误'))
  } finally {
    // 确保关闭加载状态
    if (loadingInstance) {
      loadingInstance.close()
    }
  }

  // 清空文件输入
  event.target.value = ''
}

// 保存个人信息
const handleSave = async () => {
  try {
    await basicForm.value?.validate()
    
    saving.value = true
    
    // 调用store中的更新方法
    await authStore.updateProfile(formData)
    
    // 更新备份数据
    Object.assign(originalData, formData)
    
    editMode.value = false
    ElMessage.success('个人信息更新成功')
  } catch (error) {
    console.error('保存失败:', error)
    if (error !== false) { // 不是表单验证失败
      ElMessage.error('保存失败，请稍后重试')
    }
  } finally {
    saving.value = false
  }
}

// 取消编辑
const handleCancel = () => {
  // 恢复原始数据
  Object.assign(formData, originalData)
  editMode.value = false
  basicForm.value?.clearValidate()
}

// 修改密码
const handlePasswordChange = async () => {
  try {
    await passwordForm.value?.validate()

    await ElMessageBox.confirm(
      '确定要修改密码吗？修改后需要重新登录。',
      '确认修改',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    changingPassword.value = true

    // 调用store中的修改密码方法
    await authStore.changePassword(passwordData)

    ElMessage.success('密码修改成功，正在跳转到登录页面...')

    // 清空密码表单
    resetPasswordForm()

    // 延迟1秒后直接跳转到登录页面（因为token已经在store中被清除）
    setTimeout(() => {
      router.push('/login')
    }, 1000)

  } catch (error) {
    console.error('修改密码失败:', error)
    // 只有在用户取消操作时不显示错误消息
    // 其他错误已经在响应拦截器中处理了，这里不再重复显示
    if (error === 'cancel') {
      // 用户取消操作，不显示任何消息
    } else if (error === false) {
      // 表单验证失败，不显示额外消息
    } else if (!error.showedMessage) {
      // 只有在响应拦截器没有显示错误消息时才显示
      ElMessage.error('修改密码失败，请稍后重试')
    }
  } finally {
    changingPassword.value = false
  }
}

// 重置密码表单
const resetPasswordForm = () => {
  passwordData.currentPassword = ''
  passwordData.newPassword = ''
  passwordData.confirmPassword = ''
  passwordForm.value?.clearValidate()
}

// 组件挂载时初始化数据
onMounted(async () => {
  try {
    // 检查基本认证状态并获取完整用户信息（包含统计数据）
    if (!authStore.isAuthenticated) {
      // 尝试从本地存储恢复认证状态
      const isAuthenticated = await authStore.checkAuth()

      if (!isAuthenticated) {
        ElMessage.error('请先登录系统')
        router.push('/login')
        return
      }
    } else {
      // 如果已经认证但用户信息不完整，重新获取
      if (!authStore.userInfo || !authStore.userInfo.phone) {
        await authStore.checkAuth()
      }
    }

    // 确保用户信息加载完成后再初始化表单数据
    initFormData()

    // 获取用户统计数据（现在从checkAuth中的getCurrentUser接口获取）
    try {
      await fetchUserStats()
    } catch (error) {
      console.warn('获取统计数据失败，使用默认数据:', error)
      // 使用默认统计数据，不阻止页面正常显示
      profileStats.value = {
        lastLoginTime: new Date().getTime() - 24 * 60 * 60 * 1000,
        lastActiveTime: new Date().getTime() - 2 * 60 * 60 * 1000,
        userAgent: navigator.userAgent,
        isOnline: true
      }
    }

  } catch (error) {
    console.error('初始化个人中心失败:', error)
    // 不要因为初始化失败就跳转登录，给用户更好的体验
    ElMessage.warning('部分数据加载失败，页面功能可能受限')
  }
})
</script>

<style lang="scss" scoped>
.personal-center {
  padding: 20px;
  background: #f8f9fa;
  min-height: calc(100vh - 64px);

  h1 {
    margin: 0 0 20px 0;
    font-size: 20px;
    font-weight: 500;
    color: #303133;
  }
}

.profile-header {
  background: white;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #f0f0f0;

  .profile-info {
    display: flex;
    align-items: center;
    gap: 20px;

    .user-avatar {
      border: 4px solid #f5f5f5;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .user-details {
      .username {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #303133;
      }

      .user-role {
        margin: 0 0 4px 0;
        color: #8B5CF6;
        font-size: 14px;
        font-weight: 500;
      }

      .user-id {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }

  .profile-stats {
    display: flex;
    gap: 40px;

    .stat-item {
      text-align: center;
      min-width: 120px;

      .stat-label {
        font-size: 14px;
        color: #909399;
        margin-bottom: 8px;
      }

      .stat-value {
        .percentage {
          font-size: 18px;
          font-weight: 600;
          color: #8B5CF6;
          display: block;
          margin-bottom: 4px;
        }

        .date {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
          display: block;
        }

        .time, .browser {
          font-size: 12px;
          color: #909399;
        }

        .os {
          font-size: 14px;
          color: #303133;
          display: block;
        }

        .status {
          font-size: 12px;
          
          &.online {
            color: #67C23A;
          }
        }

        :deep(.el-progress) {
          margin: 4px 0;
          
          .el-progress-bar__outer {
            background-color: #f0f0f0;
          }
        }
      }
    }
  }
}

.content-area {
  display: flex;
  gap: 24px;

  .content-left {
    flex: 1;
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #f0f0f0;

    .nav-tabs {
      display: flex;
      margin-bottom: 24px;
      border-bottom: 1px solid #e4e7ed;

      .tab-btn {
        padding: 12px 24px;
        border: none;
        background: none;
        color: #909399;
        font-size: 14px;
        cursor: pointer;
        border-bottom: 2px solid transparent;
        transition: all 0.3s;

        &:hover {
          color: #8B5CF6;
        }

        &.active {
          color: #8B5CF6;
          border-bottom-color: #8B5CF6;
        }
      }
    }

    .form-section {
      .profile-form {
        max-width: 400px;

        :deep(.el-form-item) {
          margin-bottom: 20px;

          .el-form-item__label {
            color: #606266;
            font-weight: 500;
          }

          .el-input {
            .el-input__wrapper {
              border-radius: 6px;
            }
          }
        }
      }

      .form-actions {
        margin-top: 32px;

        .el-button {
          border-radius: 8px;
          padding: 12px 24px;
          font-weight: 500;

          &.el-button--primary {
            background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
            border: none;

            &:hover {
              background: linear-gradient(135deg, #7C3AED 0%, #6D28D9 100%);
            }
          }
        }
      }
    }
  }

  .content-right {
    width: 300px;
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #f0f0f0;

    .avatar-upload-section {
      .upload-area {
        border: 2px dashed #d9d9d9;
        border-radius: 8px;
        padding: 40px 20px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          border-color: #8B5CF6;
          background: #fafafa;
        }

        .upload-icon {
          font-size: 48px;
          color: #d9d9d9;
          margin-bottom: 16px;
        }

        .upload-text {
          p {
            margin: 0 0 4px 0;
            color: #606266;
            font-size: 14px;

            &.upload-hint {
              color: #909399;
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .personal-center {
    padding: 16px;
  }

  .profile-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;

    .profile-stats {
      flex-wrap: wrap;
      gap: 20px;
      justify-content: center;
    }
  }

  .content-area {
    flex-direction: column;

    .content-right {
      width: 100%;
    }
  }
}
</style>
