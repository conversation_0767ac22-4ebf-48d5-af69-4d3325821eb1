package com.zhisuo.manager.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zhisuo.manager.common.JwtUtil;
import com.zhisuo.manager.dto.LoginRequest;
import com.zhisuo.manager.dto.UserStatsDTO;
import com.zhisuo.manager.entity.Admin;
import com.zhisuo.manager.mapper.AdminMapper;
import com.zhisuo.manager.service.AuthService;
import com.zhisuo.manager.vo.LoginResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.concurrent.TimeUnit;

/**
 * 认证服务实现类
 */
@Slf4j
@Service
public class AuthServiceImpl implements AuthService {
    
    @Autowired
    private AdminMapper adminMapper;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    @Value("${jwt.expiration}")
    private Long jwtExpiration;
    
    private static final String TOKEN_PREFIX = "manager:token:";
    
    @Override
    public LoginResponse login(LoginRequest request) {
        String username = request.getUsername();
        String password = request.getPassword();
        
        // 查询管理员信息
        Admin admin = getAdminByUsername(username);
        if (admin == null) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        // 检查账户状态
        if (admin.getStatus() == 0) {
            throw new RuntimeException("账户已被禁用");
        }
        
        // 验证密码
        log.info("输入密码: {}", password);
        log.info("数据库密码哈希: {}", admin.getPasswordHash());
        boolean passwordMatch = BCrypt.checkpw(password, admin.getPasswordHash());
        log.info("密码验证结果: {}", passwordMatch);

        if (!passwordMatch) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        // 生成JWT Token
        String token = jwtUtil.generateToken(username, admin.getAdminId());
        
        // 将Token存储到Redis
        String tokenKey = TOKEN_PREFIX + admin.getAdminId();
        redisTemplate.opsForValue().set(tokenKey, token, jwtExpiration, TimeUnit.MILLISECONDS);
        
        // 构建响应
        LoginResponse response = new LoginResponse();
        response.setAccessToken(token);
        response.setExpiresIn(jwtExpiration / 1000);
        
        LoginResponse.AdminInfo adminInfo = new LoginResponse.AdminInfo();
        adminInfo.setAdminId(admin.getAdminId());
        adminInfo.setUsername(admin.getUsername());
        adminInfo.setRealName(admin.getRealName());
        adminInfo.setEmail(admin.getEmail());
        adminInfo.setAvatar(admin.getAvatar());
        adminInfo.setRole(admin.getRole());
        response.setAdminInfo(adminInfo);
        
        log.info("管理员[{}]登录成功", username);
        return response;
    }
    
    @Override
    public void logout(String token) {
        try {
            String userId = jwtUtil.getUserIdFromToken(token);
            String tokenKey = TOKEN_PREFIX + userId;
            redisTemplate.delete(tokenKey);
            log.info("管理员[{}]登出成功", userId);
        } catch (Exception e) {
            log.error("登出失败", e);
        }
    }
    
    @Override
    public Admin getAdminByUsername(String username) {
        if (StrUtil.isBlank(username)) {
            return null;
        }
        
        LambdaQueryWrapper<Admin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Admin::getUsername, username);
        return adminMapper.selectOne(queryWrapper);
    }
    
    @Override
    public Admin getAdminById(String adminId) {
        if (StrUtil.isBlank(adminId)) {
            return null;
        }
        return adminMapper.selectById(adminId);
    }
    
    @Override
    public void updateLastLogin(String adminId, String loginIp) {
        LambdaUpdateWrapper<Admin> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Admin::getAdminId, adminId)
                .set(Admin::getLastLoginTime, LocalDateTime.now())
                .set(Admin::getLastLoginIp, loginIp);
        adminMapper.update(null, updateWrapper);
    }

    @Override
    public UserStatsDTO getUserStats(String username, HttpServletRequest request) {
        Admin admin = getAdminByUsername(username);
        if (admin == null) {
            throw new RuntimeException("用户不存在");
        }

        UserStatsDTO stats = new UserStatsDTO();

        // 设置最后登录时间
        if (admin.getLastLoginTime() != null) {
            stats.setLastLoginTime(admin.getLastLoginTime().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }

        // 设置最后活跃时间（当前时间）
        stats.setLastActiveTime(System.currentTimeMillis());

        // 设置用户代理信息
        stats.setUserAgent(request.getHeader("User-Agent"));

        // 设置在线状态（简单判断，实际可以根据业务需求调整）
        stats.setIsOnline(true);

        // 设置账号创建时间
        if (admin.getCreateTime() != null) {
            stats.setCreateTime(admin.getCreateTime().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }

        // 设置IP地址
        stats.setIpAddress(admin.getLastLoginIp());

        return stats;
    }

    @Override
    public void updateProfile(String username, Admin admin) {
        Admin existingAdmin = getAdminByUsername(username);
        if (existingAdmin == null) {
            throw new RuntimeException("用户不存在");
        }

        LambdaUpdateWrapper<Admin> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Admin::getUsername, username);

        // 只更新允许修改的字段
        if (StrUtil.isNotBlank(admin.getRealName())) {
            updateWrapper.set(Admin::getRealName, admin.getRealName());
        }
        if (StrUtil.isNotBlank(admin.getEmail())) {
            updateWrapper.set(Admin::getEmail, admin.getEmail());
        }
        if (StrUtil.isNotBlank(admin.getPhone())) {
            updateWrapper.set(Admin::getPhone, admin.getPhone());
        }
        if (StrUtil.isNotBlank(admin.getAvatar())) {
            updateWrapper.set(Admin::getAvatar, admin.getAvatar());
        }

        updateWrapper.set(Admin::getUpdateTime, LocalDateTime.now());

        adminMapper.update(null, updateWrapper);
    }

    @Override
    public void changePassword(String username, String currentPassword, String newPassword) {
        Admin admin = getAdminByUsername(username);
        if (admin == null) {
            throw new RuntimeException("用户不存在");
        }

        // 验证当前密码
        if (!BCrypt.checkpw(currentPassword, admin.getPasswordHash())) {
            throw new RuntimeException("当前密码错误");
        }

        // 加密新密码
        String encodedPassword = BCrypt.hashpw(newPassword, BCrypt.gensalt());

        // 更新密码
        LambdaUpdateWrapper<Admin> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Admin::getUsername, username)
                .set(Admin::getPasswordHash, encodedPassword)
                .set(Admin::getUpdateTime, LocalDateTime.now());

        int result = adminMapper.update(null, updateWrapper);

        // 如果更新成功，使当前用户的所有token失效，强制重新登录
        if (result > 0) {
            // 删除Redis中该用户的所有token
            String tokenKey = TOKEN_PREFIX + admin.getAdminId();
            redisTemplate.delete(tokenKey);

            log.info("管理员[{}]修改密码成功，已清除token", username);
        }
    }

    @Override
    public void resetAdminPassword() {
        try {
            String username = "admin";
            String password = "admin123";

            // 生成新的密码哈希
            String passwordHash = BCrypt.hashpw(password, BCrypt.gensalt());

            log.info("重置admin用户密码");
            log.info("新密码: {}", password);
            log.info("新密码哈希: {}", passwordHash);

            // 更新数据库
            LambdaUpdateWrapper<Admin> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Admin::getUsername, username)
                    .set(Admin::getPasswordHash, passwordHash)
                    .set(Admin::getUpdateTime, LocalDateTime.now());

            int result = adminMapper.update(null, updateWrapper);

            if (result > 0) {
                log.info("admin用户密码重置成功");

                // 验证密码是否正确
                boolean isValid = BCrypt.checkpw(password, passwordHash);
                log.info("密码验证结果: {}", isValid);
            } else {
                log.error("admin用户密码重置失败");
                throw new RuntimeException("密码重置失败");
            }

        } catch (Exception e) {
            log.error("重置密码时发生错误", e);
            throw new RuntimeException("重置密码失败: " + e.getMessage());
        }
    }
}
