import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { getToken } from '@/utils/auth'
import router from '@/router'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/manager',
  timeout: 10000
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 添加认证token
    const token = getToken()
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }

    // 如果是 FormData，不设置 Content-Type，让浏览器自动设置
    // 否则设置为 application/json
    if (!(config.data instanceof FormData)) {
      config.headers['Content-Type'] = 'application/json'
    }

    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data

    // 如果返回的状态码不是200，则显示错误信息
    if (res.code !== 200) {
      // 401: 未授权
      if (res.code === 401) {
        // 避免重复显示未授权提示
        if (!window.isShowingUnauthorizedDialog) {
          window.isShowingUnauthorizedDialog = true
          ElMessageBox.confirm(
            '登录状态已过期，请重新登录',
            '系统提示',
            {
              confirmButtonText: '重新登录',
              cancelButtonText: '取消',
              type: 'warning'
            }
          ).then(() => {
            const authStore = useAuthStore()
            authStore.logout().then(() => {
              router.push('/login')
            })
          }).finally(() => {
            window.isShowingUnauthorizedDialog = false
          })
        }
      } else {
        // 对于非401错误，显示错误消息
        ElMessage({
          message: res.message || '请求失败',
          type: 'error',
          duration: 5000
        })
      }

      // 创建一个带有message属性的错误对象，这样组件可以检测到并避免重复显示
      const error = new Error(res.message || '请求失败')
      error.code = res.code
      error.showedMessage = true // 标记已经显示过错误消息
      return Promise.reject(error)
    } else {
      return res
    }
  },
  error => {
    console.error('响应错误:', error)
    
    let message = '网络错误'
    if (error.response) {
      switch (error.response.status) {
        case 400:
          message = '请求参数错误'
          break
        case 401:
          message = '未授权，请重新登录'
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求地址不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = `连接错误${error.response.status}`
      }
    } else if (error.code === 'ECONNABORTED') {
      message = '请求超时'
    } else if (error.message.includes('Network Error')) {
      message = '网络连接异常'
    }
    
    ElMessage({
      message,
      type: 'error',
      duration: 5000
    })
    
    return Promise.reject(error)
  }
)

export default service
